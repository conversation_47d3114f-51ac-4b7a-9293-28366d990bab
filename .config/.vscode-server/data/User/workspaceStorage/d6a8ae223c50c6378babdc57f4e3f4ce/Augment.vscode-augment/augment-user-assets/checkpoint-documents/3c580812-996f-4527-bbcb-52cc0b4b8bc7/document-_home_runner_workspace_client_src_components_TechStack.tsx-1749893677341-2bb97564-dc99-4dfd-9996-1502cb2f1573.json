{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/TechStack.tsx"}, "originalCode": "import React from 'react';\nimport { Globe } from 'lucide-react';\n\nconst TechStack: React.FC = () => {\n\n  return (\n    <section id=\"tech\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Technologies</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions for Australian businesses\n          </p>\n        </div>\n\n\n\n        {/* Bottom Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6\">\n            <Globe className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            <span className=\"text-gray-700 dark:text-gray-300 font-medium\">Always Learning, Always Innovating</span>\n          </div>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TechStack;", "modifiedCode": "import React from 'react';\nimport { Globe } from 'lucide-react';\n\nconst TechStack: React.FC = () => {\n\n  return (\n    <section id=\"tech\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Technologies</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions for Australian businesses\n          </p>\n        </div>\n\n\n\n        {/* Bottom Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6\">\n            <Globe className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            <span className=\"text-gray-700 dark:text-gray-300 font-medium\">Always Learning, Always Innovating</span>\n          </div>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TechStack;"}