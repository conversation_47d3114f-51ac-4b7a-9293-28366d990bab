{"uuid": "20a4ba8f-5be0-4c5d-85c0-2590b7330121", "name": "Automation assesment", "description": "when I click on the calculate my automation savings button I want the page that shows up to not need to scroll. It looks like maybe a better visual way to have the list instead of radio buttons a sort of drop down menu that allows you to select multiple options would be good. Conserve real estate and make sure the size of the modal box is standadrised between the quick quote and the automation assesment i want everything to feel unified and consistent across the whole website expeirence.", "state": "COMPLETE", "subTasks": [], "lastUpdated": 1749894925906, "lastUpdatedBy": "USER"}