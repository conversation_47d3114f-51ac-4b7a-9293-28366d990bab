{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Contact.tsx"}, "originalCode": "import React, { useState, useCallback } from \"react\";\nimport {\n  Mail,\n  Phone,\n  MapPin,\n  Send,\n  CheckCircle,\n  MessageSquare,\n  Calendar,\n  Linkedin,\n  Twitter,\n  Github,\n  Loader2,\n  AlertCircle,\n} from \"lucide-react\";\nimport { useForm } from \"../hooks/useForm\";\nimport { validateContactForm, type ContactFormData } from \"../utils/validation\";\nimport { submitContactForm } from \"../utils/emailService\";\nimport { openChat } from \"../utils/chatbot\";\n\nconst Contact: React.FC = () => {\n  const {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n  } = useForm<ContactFormData>({\n    initialValues: {\n      name: \"\",\n      email: \"\",\n      company: \"\",\n      project: \"\",\n      budget: \"\",\n      message: \"\",\n    },\n    validate: (values) => validateContactForm(values).errors,\n    onSubmit: async (values) => {\n      const result = await submitContactForm(values);\n      return result;\n    },\n  });\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Let's Build Something{\" \"}\n            <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\n              Amazing\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Ready to transform your business? Start with a free 30-minute discovery call with a senior engineer.\n            Fixed-price quotes, Australian support, and your data stays onshore.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n              Start Your Project\n            </h3>\n\n            {isSubmitted ? (\n              <div className=\"text-center py-12\">\n                <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <CheckCircle className=\"h-8 w-8 text-green-600 dark:text-green-400\" />\n                </div>\n                <h4 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                  Thank You!\n                </h4>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  {submitMessage}\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label\n                      htmlFor=\"name\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Full Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      value={values.name}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.name\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      placeholder=\"John Doe\"\n                      disabled={isSubmitting}\n                    />\n                    {errors.name && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.name}\n                      </p>\n                    )}\n                  </div>\n                  <div>\n                    <label\n                      htmlFor=\"email\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Email Address *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      required\n                      value={values.email}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.email\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      placeholder=\"<EMAIL>\"\n                      disabled={isSubmitting}\n                    />\n                    {errors.email && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.email}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"company\"\n                    className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                  >\n                    Company Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={values.company}\n                    onChange={handleChange}\n                    className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                      errors.company\n                        ? \"border-red-500 dark:border-red-500\"\n                        : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Your Company\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.company && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-1\" />\n                      {errors.company}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label\n                      htmlFor=\"project\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Project Type\n                    </label>\n                    <select\n                      id=\"project\"\n                      name=\"project\"\n                      value={values.project}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.project\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      disabled={isSubmitting}\n                    >\n                      <option value=\"\">Select a service</option>\n                      <option value=\"web-design\">\n                        Web Design & Development\n                      </option>\n                      <option value=\"automation\">Business Automation</option>\n                      <option value=\"ai\">AI Solutions</option>\n                      <option value=\"small-business\">Small Business Essentials</option>\n                      <option value=\"consulting\">Consulting</option>\n                    </select>\n                    {errors.project && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.project}\n                      </p>\n                    )}\n                  </div>\n                  <div>\n                    <label\n                      htmlFor=\"budget\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Budget Range\n                    </label>\n                    <select\n                      id=\"budget\"\n                      name=\"budget\"\n                      value={values.budget}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.budget\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      disabled={isSubmitting}\n                    >\n                      <option value=\"\">Select budget</option>\n                      <option value=\"5k-15k\">$5K - $15K</option>\n                      <option value=\"15k-50k\">$15K - $50K</option>\n                      <option value=\"50k-100k\">$50K - $100K</option>\n                      <option value=\"100k+\">$100K+</option>\n                    </select>\n                    {errors.budget && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.budget}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"message\"\n                    className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                  >\n                    Project Details *\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    required\n                    rows={5}\n                    value={values.message}\n                    onChange={handleChange}\n                    className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                      errors.message\n                        ? \"border-red-500 dark:border-red-500\"\n                        : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Tell us about your project, goals, and any specific requirements...\"\n                    disabled={isSubmitting}\n                  ></textarea>\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-1\" />\n                      {errors.message}\n                    </p>\n                  )}\n                </div>\n\n                {submitMessage && !isSubmitted && (\n                  <div\n                    className={`p-4 rounded-lg ${\n                      submitMessage.includes(\"error\") ||\n                      submitMessage.includes(\"Sorry\")\n                        ? \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400\"\n                        : \"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400\"\n                    }`}\n                  >\n                    <p className=\"flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-2\" />\n                      {submitMessage}\n                    </p>\n                  </div>\n                )}\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className={`w-full inline-flex items-center justify-center px-8 py-4 font-semibold rounded-lg shadow-lg transition-all duration-300 ${\n                    isSubmitting\n                      ? \"bg-gray-400 cursor-not-allowed\"\n                      : \"bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-xl transform hover:-translate-y-1\"\n                  } text-white`}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                      Sending...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"mr-2 h-5 w-5\" />\n                      Send Message\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"space-y-8\">\n            {/* Contact Details */}\n            <div>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                Get In Touch\n              </h3>\n\n              <div className=\"space-y-6\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg\">\n                    <Mail className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Email\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      <EMAIL>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\">\n                    <Phone className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Phone\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      +61 8 9757 2000\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg\">\n                    <MapPin className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Location\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      Margaret River and Canberra, Australia\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Australian Guarantees */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-2xl p-6 border border-green-200 dark:border-green-800\">\n              <h4 className=\"text-lg font-semibold text-green-800 dark:text-green-300 mb-4\">Our Australian Guarantees</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span className=\"text-sm text-green-700 dark:text-green-300\">24-hour response time on all support tickets</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span className=\"text-sm text-green-700 dark:text-green-300\">Fixed-price quotes with no hidden fees</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span className=\"text-sm text-green-700 dark:text-green-300\">Your data hosted exclusively in Australian data centres</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 text-white\">\n              <h4 className=\"text-xl font-bold mb-4\">Quick Actions</h4>\n              <div className=\"space-y-3\">\n                <button\n                  onClick={() => {\n                    // Try Botpress first, fallback to scrolling to chat bubble\n                    if (window.botpress && typeof window.botpress.open === 'function') {\n                      openChat();\n                    } else {\n                      // Fallback: trigger the custom chat bubble\n                      const chatBubble = document.querySelector('[aria-label=\"Open chat\"]') as HTMLButtonElement;\n                      if (chatBubble) {\n                        chatBubble.click();\n                      } else {\n                        alert('Chat is available! Look for the purple chat bubble in the bottom-right corner of your screen.');\n                      }\n                    }\n                  }}\n                  className=\"w-full flex items-center space-x-3 bg-white/20 hover:bg-white/30 rounded-lg p-3 transition-colors duration-200\"\n                >\n                  <MessageSquare className=\"h-5 w-5\" />\n                  <span>Start Live Chat</span>\n                </button>\n                <button className=\"w-full flex items-center space-x-3 bg-white/20 hover:bg-white/30 rounded-lg p-3 transition-colors duration-200\">\n                  <Calendar className=\"h-5 w-5\" />\n                  <span>Schedule a Call</span>\n                </button>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                Follow Us\n              </h4>\n              <div className=\"flex space-x-4\">\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-purple-100 dark:hover:bg-purple-900 rounded-lg text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200\">\n                  <Linkedin className=\"h-6 w-6\" />\n                </button>\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900 rounded-lg text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\">\n                  <Twitter className=\"h-6 w-6\" />\n                </button>\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200\">\n                  <Github className=\"h-6 w-6\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n", "modifiedCode": "import React, { useState, useCallback } from \"react\";\nimport {\n  Mail,\n  Phone,\n  MapPin,\n  Send,\n  CheckCircle,\n  MessageSquare,\n  Calendar,\n  Linkedin,\n  Twitter,\n  Github,\n  Loader2,\n  AlertCircle,\n} from \"lucide-react\";\nimport { useForm } from \"../hooks/useForm\";\nimport { validateContactForm, type ContactFormData } from \"../utils/validation\";\nimport { submitContactForm } from \"../utils/emailService\";\nimport { openChat } from \"../utils/chatbot\";\n\nconst Contact: React.FC = () => {\n  const {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n  } = useForm<ContactFormData>({\n    initialValues: {\n      name: \"\",\n      email: \"\",\n      company: \"\",\n      project: \"\",\n      budget: \"\",\n      message: \"\",\n    },\n    validate: (values) => validateContactForm(values).errors,\n    onSubmit: async (values) => {\n      const result = await submitContactForm(values);\n      return result;\n    },\n  });\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Let's Build Something{\" \"}\n            <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\n              Amazing\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            Ready to transform your business? Start with a free 30-minute discovery call with a senior engineer.\n            Fixed-price quotes, Australian support, and your data stays onshore.\n          </p>\n        </div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n              Start Your Project\n            </h3>\n\n            {isSubmitted ? (\n              <div className=\"text-center py-12\">\n                <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <CheckCircle className=\"h-8 w-8 text-green-600 dark:text-green-400\" />\n                </div>\n                <h4 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                  Thank You!\n                </h4>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  {submitMessage}\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label\n                      htmlFor=\"name\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Full Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      value={values.name}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.name\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      placeholder=\"John Doe\"\n                      disabled={isSubmitting}\n                    />\n                    {errors.name && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.name}\n                      </p>\n                    )}\n                  </div>\n                  <div>\n                    <label\n                      htmlFor=\"email\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Email Address *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      required\n                      value={values.email}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.email\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      placeholder=\"<EMAIL>\"\n                      disabled={isSubmitting}\n                    />\n                    {errors.email && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.email}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"company\"\n                    className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                  >\n                    Company Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={values.company}\n                    onChange={handleChange}\n                    className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                      errors.company\n                        ? \"border-red-500 dark:border-red-500\"\n                        : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Your Company\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.company && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-1\" />\n                      {errors.company}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label\n                      htmlFor=\"project\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Project Type\n                    </label>\n                    <select\n                      id=\"project\"\n                      name=\"project\"\n                      value={values.project}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.project\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      disabled={isSubmitting}\n                    >\n                      <option value=\"\">Select a service</option>\n                      <option value=\"web-design\">\n                        Web Design & Development\n                      </option>\n                      <option value=\"automation\">Business Automation</option>\n                      <option value=\"ai\">AI Solutions</option>\n                      <option value=\"small-business\">Small Business Essentials</option>\n                      <option value=\"consulting\">Consulting</option>\n                    </select>\n                    {errors.project && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.project}\n                      </p>\n                    )}\n                  </div>\n                  <div>\n                    <label\n                      htmlFor=\"budget\"\n                      className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                    >\n                      Budget Range\n                    </label>\n                    <select\n                      id=\"budget\"\n                      name=\"budget\"\n                      value={values.budget}\n                      onChange={handleChange}\n                      className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                        errors.budget\n                          ? \"border-red-500 dark:border-red-500\"\n                          : \"border-gray-300 dark:border-gray-600\"\n                      }`}\n                      disabled={isSubmitting}\n                    >\n                      <option value=\"\">Select budget</option>\n                      <option value=\"5k-15k\">$5K - $15K</option>\n                      <option value=\"15k-50k\">$15K - $50K</option>\n                      <option value=\"50k-100k\">$50K - $100K</option>\n                      <option value=\"100k+\">$100K+</option>\n                    </select>\n                    {errors.budget && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.budget}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"message\"\n                    className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n                  >\n                    Project Details *\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    required\n                    rows={5}\n                    value={values.message}\n                    onChange={handleChange}\n                    className={`w-full px-4 py-3 bg-white dark:bg-gray-700 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white ${\n                      errors.message\n                        ? \"border-red-500 dark:border-red-500\"\n                        : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Tell us about your project, goals, and any specific requirements...\"\n                    disabled={isSubmitting}\n                  ></textarea>\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-1\" />\n                      {errors.message}\n                    </p>\n                  )}\n                </div>\n\n                {submitMessage && !isSubmitted && (\n                  <div\n                    className={`p-4 rounded-lg ${\n                      submitMessage.includes(\"error\") ||\n                      submitMessage.includes(\"Sorry\")\n                        ? \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400\"\n                        : \"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400\"\n                    }`}\n                  >\n                    <p className=\"flex items-center\">\n                      <AlertCircle className=\"h-4 w-4 mr-2\" />\n                      {submitMessage}\n                    </p>\n                  </div>\n                )}\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className={`w-full inline-flex items-center justify-center px-8 py-4 font-semibold rounded-lg shadow-lg transition-all duration-300 ${\n                    isSubmitting\n                      ? \"bg-gray-400 cursor-not-allowed\"\n                      : \"bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-xl transform hover:-translate-y-1\"\n                  } text-white`}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                      Sending...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"mr-2 h-5 w-5\" />\n                      Send Message\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"space-y-8\">\n            {/* Contact Details */}\n            <div>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                Get In Touch\n              </h3>\n\n              <div className=\"space-y-6\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg\">\n                    <Mail className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Email\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      <EMAIL>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\">\n                    <Phone className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Phone\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      +61 8 9757 2000\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg\">\n                    <MapPin className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900 dark:text-white\">\n                      Location\n                    </div>\n                    <div className=\"text-gray-600 dark:text-gray-300\">\n                      Margaret River and Canberra, Australia\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* Quick Actions */}\n            <div className=\"bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 text-white\">\n              <h4 className=\"text-xl font-bold mb-4\">Quick Actions</h4>\n              <div className=\"space-y-3\">\n                <button\n                  onClick={() => {\n                    // Try Botpress first, fallback to scrolling to chat bubble\n                    if (window.botpress && typeof window.botpress.open === 'function') {\n                      openChat();\n                    } else {\n                      // Fallback: trigger the custom chat bubble\n                      const chatBubble = document.querySelector('[aria-label=\"Open chat\"]') as HTMLButtonElement;\n                      if (chatBubble) {\n                        chatBubble.click();\n                      } else {\n                        alert('Chat is available! Look for the purple chat bubble in the bottom-right corner of your screen.');\n                      }\n                    }\n                  }}\n                  className=\"w-full flex items-center space-x-3 bg-white/20 hover:bg-white/30 rounded-lg p-3 transition-colors duration-200\"\n                >\n                  <MessageSquare className=\"h-5 w-5\" />\n                  <span>Start Live Chat</span>\n                </button>\n                <button className=\"w-full flex items-center space-x-3 bg-white/20 hover:bg-white/30 rounded-lg p-3 transition-colors duration-200\">\n                  <Calendar className=\"h-5 w-5\" />\n                  <span>Schedule a Call</span>\n                </button>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                Follow Us\n              </h4>\n              <div className=\"flex space-x-4\">\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-purple-100 dark:hover:bg-purple-900 rounded-lg text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200\">\n                  <Linkedin className=\"h-6 w-6\" />\n                </button>\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900 rounded-lg text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\">\n                  <Twitter className=\"h-6 w-6\" />\n                </button>\n                <button className=\"p-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200\">\n                  <Github className=\"h-6 w-6\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"}