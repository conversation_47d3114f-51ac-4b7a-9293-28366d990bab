{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AutomationAssessmentModal.tsx"}, "originalCode": "import React, { useState } from 'react';\nimport { X, <PERSON><PERSON><PERSON><PERSON>, Calculator, ArrowRight, ArrowLeft, Loader2 } from 'lucide-react';\nimport { useForm } from '../hooks/useForm';\nimport { validateAutomationAssessmentForm, type AutomationAssessmentFormData } from '../utils/validation';\nimport { submitAutomationAssessmentForm } from '../utils/emailService';\n\ninterface AutomationAssessmentModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst AutomationAssessmentModal: React.FC<AutomationAssessmentModalProps> = ({ isOpen, onClose }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 4;\n\n  const {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n    setFieldValue,\n    resetForm\n  } = useForm<AutomationAssessmentFormData>({\n    initialValues: {\n      businessType: '',\n      companySize: '',\n      biggestChallenge: '',\n      primaryTimeWaster: '',\n      hoursPerWeek: 5,\n      peopleInvolved: 1,\n      hourlyRate: 50,\n      automationConfidence: '',\n      email: '',\n      phone: '',\n      companyName: '',\n      bestTimeToContact: ''\n    },\n    validate: (values) => validateAutomationAssessmentForm(values, currentStep).errors,\n    onSubmit: async (values) => {\n      const result = await submitAutomationAssessmentForm(values);\n      return result;\n    }\n  });\n\n  // Handle modal close\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n      setTimeout(() => {\n        setCurrentStep(1);\n        resetForm();\n      }, 300);\n    }\n  };\n\n  // Handle backdrop click\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  // Navigation functions\n  const goToNextStep = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const goToPreviousStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Calculate potential savings\n  const calculateSavings = () => {\n    const weeklyHours = values.hoursPerWeek;\n    const hourlyRate = values.hourlyRate;\n    const people = values.peopleInvolved;\n    \n    const weeklySavings = weeklyHours * hourlyRate * people * 0.7; // 70% automation efficiency\n    const monthlySavings = weeklySavings * 4.33;\n    const annualSavings = monthlySavings * 12;\n    \n    return {\n      weekly: Math.round(weeklySavings),\n      monthly: Math.round(monthlySavings),\n      annual: Math.round(annualSavings),\n      hoursPerWeek: Math.round(weeklyHours * people * 0.7)\n    };\n  };\n\n  // Form submission handler\n  const onFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Validate current step\n    const stepErrors = validateAutomationAssessmentForm(values, currentStep).errors;\n    \n    if (Object.keys(stepErrors).length === 0) {\n      if (currentStep < totalSteps) {\n        goToNextStep();\n      } else {\n        // Final submission\n        handleSubmit(e);\n      }\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div \n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n      onClick={handleBackdropClick}\n    >\n      <div \n        className=\"relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100 max-h-[90vh] overflow-hidden\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Automation Assessment\n            </h2>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n              Step {currentStep} of {totalSteps}\n            </p>\n          </div>\n          <button\n            onClick={handleClose}\n            disabled={isSubmitting}\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50\"\n          >\n            <X className=\"h-6 w-6 text-gray-500 dark:text-gray-400\" />\n          </button>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-800/50\">\n          <div className=\"flex items-center space-x-2\">\n            {Array.from({ length: totalSteps }, (_, i) => (\n              <div key={i} className=\"flex-1\">\n                <div className={`h-2 rounded-full transition-colors duration-300 ${\n                  i + 1 <= currentStep \n                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500' \n                    : 'bg-gray-200 dark:bg-gray-700'\n                }`} />\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[65vh]\">\n          {isSubmitted ? (\n            // Success State\n            <div className=\"text-center py-8\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-full mb-6\">\n                <CheckCircle className=\"h-8 w-8 text-blue-500\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                🚀 Your Automation Journey Starts Here!\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-6 leading-relaxed\">\n                Congratulations! You've taken the first step toward transforming your business with intelligent automation. \n                Based on your responses, we can see significant opportunities to save you time and reduce costs.\n              </p>\n              \n              <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6 mb-6\">\n                <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">What happens next:</h4>\n                <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span><strong>Within 24 hours:</strong> Personalized automation report in your inbox</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-cyan-500 rounded-full\"></div>\n                    <span><strong>Within 2 business days:</strong> Follow-up call to discuss your opportunities</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span><strong>Within 1 week:</strong> Custom automation proposal with fixed pricing</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-6\">\n                🇦🇺 Proudly Australian-owned • Over 40 years combined automation experience • Transparent pricing, local support, onshore data\n              </div>\n\n              <button\n                onClick={handleClose}\n                className=\"inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                Close\n              </button>\n            </div>\n          ) : (\n            // Form Steps\n            <form onSubmit={onFormSubmit} className=\"space-y-6\">\n              {/* Step 1: Business Assessment */}\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <div className=\"text-center mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                      Tell Us About Your Business\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      Understanding your current situation helps us identify the best automation opportunities\n                    </p>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Business Type *\n                      </label>\n                      <select\n                        name=\"businessType\"\n                        value={values.businessType}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        <option value=\"\">Select your industry...</option>\n                        <option value=\"professional-services\">Professional Services</option>\n                        <option value=\"retail-ecommerce\">Retail/E-commerce</option>\n                        <option value=\"manufacturing\">Manufacturing</option>\n                        <option value=\"healthcare\">Healthcare</option>\n                        <option value=\"real-estate\">Real Estate</option>\n                        <option value=\"hospitality\">Hospitality</option>\n                        <option value=\"other\">Other</option>\n                      </select>\n                      {errors.businessType && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.businessType}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Company Size *\n                      </label>\n                      <select\n                        name=\"companySize\"\n                        value={values.companySize}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        <option value=\"\">Select size...</option>\n                        <option value=\"just-me\">Just me (1 person)</option>\n                        <option value=\"small-team\">Small team (2-10 people)</option>\n                        <option value=\"growing-business\">Growing business (11-50 people)</option>\n                        <option value=\"established-company\">Established company (50+ people)</option>\n                      </select>\n                      {errors.companySize && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.companySize}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Current Biggest Challenge *\n                    </label>\n                    <select\n                      name=\"biggestChallenge\"\n                      value={values.biggestChallenge}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your biggest challenge...</option>\n                      <option value=\"manual-data-entry\">Too much manual data entry</option>\n                      <option value=\"admin-tasks\">Repetitive administrative tasks</option>\n                      <option value=\"system-communication\">Poor communication between systems</option>\n                      <option value=\"reporting\">Time-consuming reporting</option>\n                      <option value=\"customer-followup\">Customer follow-up processes</option>\n                      <option value=\"inventory-scheduling\">Inventory/scheduling management</option>\n                    </select>\n                    {errors.biggestChallenge && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.biggestChallenge}</p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Automation Opportunities */}\n              {currentStep === 2 && (\n                <div className=\"space-y-6\">\n                  <div className=\"text-center mb-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      Where Do You Spend Most of Your Time?\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      Let's identify your biggest time drains that automation could eliminate\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Primary Time Waster *\n                    </label>\n                    <select\n                      name=\"primaryTimeWaster\"\n                      value={values.primaryTimeWaster}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your primary time waster...</option>\n                      <option value=\"administrative-tasks\">Administrative Tasks (invoicing, scheduling, file management)</option>\n                      <option value=\"customer-communications\">Customer Communications (follow-ups, reminders, support)</option>\n                      <option value=\"marketing-social\">Marketing & Social Media (content posting, lead nurturing)</option>\n                      <option value=\"data-entry-reporting\">Data Entry & Reporting (manual input, generating reports)</option>\n                      <option value=\"inventory-project\">Inventory/Project Management (tracking, updates, coordination)</option>\n                    </select>\n                    {errors.primaryTimeWaster && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.primaryTimeWaster}</p>\n                    )}\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Hours per week spent on this *\n                      </label>\n                      <div className=\"space-y-2\">\n                        <input\n                          type=\"range\"\n                          name=\"hoursPerWeek\"\n                          min=\"1\"\n                          max=\"40\"\n                          value={values.hoursPerWeek}\n                          onChange={handleChange}\n                          className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider\"\n                        />\n                        <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n                          <span>1 hour</span>\n                          <span className=\"font-medium text-blue-600 dark:text-blue-400\">{values.hoursPerWeek} hours</span>\n                          <span>40+ hours</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        How many people are involved? *\n                      </label>\n                      <select\n                        name=\"peopleInvolved\"\n                        value={values.peopleInvolved}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        {Array.from({ length: 10 }, (_, i) => (\n                          <option key={i + 1} value={i + 1}>\n                            {i + 1} {i === 0 ? 'person' : 'people'}\n                          </option>\n                        ))}\n                        <option value=\"10+\">10+ people</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Impact Estimation */}\n              {currentStep === 3 && (\n                <div className=\"space-y-6\">\n                  <div className=\"text-center mb-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      Let's Calculate Your Potential Savings\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      Based on your responses, here's what automation could save you\n                    </p>\n                  </div>\n\n                  <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6\">\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-4\">Time Investment Input:</h4>\n                    <div className=\"grid md:grid-cols-3 gap-4 mb-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Hours per week\n                        </label>\n                        <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                          {values.hoursPerWeek}\n                        </div>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Your hourly rate ($) *\n                        </label>\n                        <input\n                          type=\"number\"\n                          name=\"hourlyRate\"\n                          value={values.hourlyRate}\n                          onChange={handleChange}\n                          min=\"1\"\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                          required\n                        />\n                        {errors.hourlyRate && (\n                          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.hourlyRate}</p>\n                        )}\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Team members affected\n                        </label>\n                        <div className=\"text-2xl font-bold text-cyan-600 dark:text-cyan-400\">\n                          {values.peopleInvolved}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                      <h4 className=\"font-semibold text-gray-900 dark:text-white mb-4\">Potential Savings Preview:</h4>\n                      <div className=\"grid md:grid-cols-3 gap-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().weekly.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Weekly savings</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().monthly.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Monthly savings</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().annual.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Annual savings</div>\n                        </div>\n                      </div>\n                      <div className=\"text-center mt-4 text-sm text-gray-600 dark:text-gray-400\">\n                        <strong>{calculateSavings().hoursPerWeek} hours per week</strong> could be automated\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Automation Confidence Level *\n                    </label>\n                    <select\n                      name=\"automationConfidence\"\n                      value={values.automationConfidence}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your confidence level...</option>\n                      <option value=\"ready-now\">I'm ready to automate now</option>\n                      <option value=\"learn-more\">I want to learn more first</option>\n                      <option value=\"need-help\">I need help understanding the options</option>\n                    </select>\n                    {errors.automationConfidence && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.automationConfidence}</p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Contact Capture & Results */}\n              {currentStep === 4 && (\n                <div className=\"space-y-6\">\n                  <div className=\"text-center mb-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      Get Your Personalized Automation Report\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      We'll send you a detailed analysis plus next steps for your automation journey\n                    </p>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email Address *\n                      </label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={values.email}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"<EMAIL>\"\n                        required\n                      />\n                      {errors.email && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.email}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Phone Number\n                      </label>\n                      <input\n                        type=\"tel\"\n                        name=\"phone\"\n                        value={values.phone}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"+61 4XX XXX XXX\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Company Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        name=\"companyName\"\n                        value={values.companyName}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"Your Company Pty Ltd\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Best time to contact\n                      </label>\n                      <select\n                        name=\"bestTimeToContact\"\n                        value={values.bestTimeToContact}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      >\n                        <option value=\"\">Select preferred time...</option>\n                        <option value=\"morning\">Morning (9am-12pm)</option>\n                        <option value=\"afternoon\">Afternoon (12pm-5pm)</option>\n                        <option value=\"evening\">Evening (5pm-7pm)</option>\n                        <option value=\"weekends\">Weekends</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/10 dark:to-blue-900/10 rounded-lg p-6\">\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">Australian Value Promise:</h4>\n                    <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                        <span>Fixed-price automation quotes - no surprises</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span>Your data stays in Australian data centres</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                        <span>Free 30-minute strategy call with a senior automation engineer</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span>Serving businesses from Margaret River to Canberra</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Navigation Buttons */}\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700\">\n                <button\n                  type=\"button\"\n                  onClick={goToPreviousStep}\n                  disabled={currentStep === 1 || isSubmitting}\n                  className={`inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors duration-200 ${\n                    currentStep === 1 || isSubmitting\n                      ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Previous\n                </button>\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Processing...\n                    </>\n                  ) : currentStep === totalSteps ? (\n                    <>\n                      <Calculator className=\"h-4 w-4 mr-2\" />\n                      Get My Report\n                    </>\n                  ) : (\n                    <>\n                      Next Step\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AutomationAssessmentModal;\n", "modifiedCode": "import React, { useState } from 'react';\nimport { X, <PERSON><PERSON><PERSON><PERSON>, Calculator, ArrowRight, ArrowLeft, Loader2 } from 'lucide-react';\nimport { useForm } from '../hooks/useForm';\nimport { validateAutomationAssessmentForm, type AutomationAssessmentFormData } from '../utils/validation';\nimport { submitAutomationAssessmentForm } from '../utils/emailService';\n\ninterface AutomationAssessmentModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst AutomationAssessmentModal: React.FC<AutomationAssessmentModalProps> = ({ isOpen, onClose }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 4;\n\n  const {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n    setFieldValue,\n    resetForm\n  } = useForm<AutomationAssessmentFormData>({\n    initialValues: {\n      businessType: '',\n      companySize: '',\n      biggestChallenge: '',\n      primaryTimeWaster: '',\n      hoursPerWeek: 5,\n      peopleInvolved: 1,\n      hourlyRate: 50,\n      automationConfidence: '',\n      email: '',\n      phone: '',\n      companyName: '',\n      bestTimeToContact: ''\n    },\n    validate: (values) => validateAutomationAssessmentForm(values, currentStep).errors,\n    onSubmit: async (values) => {\n      const result = await submitAutomationAssessmentForm(values);\n      return result;\n    }\n  });\n\n  // Handle modal close\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n      setTimeout(() => {\n        setCurrentStep(1);\n        resetForm();\n      }, 300);\n    }\n  };\n\n  // Handle backdrop click\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget) {\n      handleClose();\n    }\n  };\n\n  // Navigation functions\n  const goToNextStep = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const goToPreviousStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Calculate potential savings\n  const calculateSavings = () => {\n    const weeklyHours = values.hoursPerWeek;\n    const hourlyRate = values.hourlyRate;\n    const people = values.peopleInvolved;\n    \n    const weeklySavings = weeklyHours * hourlyRate * people * 0.7; // 70% automation efficiency\n    const monthlySavings = weeklySavings * 4.33;\n    const annualSavings = monthlySavings * 12;\n    \n    return {\n      weekly: Math.round(weeklySavings),\n      monthly: Math.round(monthlySavings),\n      annual: Math.round(annualSavings),\n      hoursPerWeek: Math.round(weeklyHours * people * 0.7)\n    };\n  };\n\n  // Form submission handler\n  const onFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Validate current step\n    const stepErrors = validateAutomationAssessmentForm(values, currentStep).errors;\n    \n    if (Object.keys(stepErrors).length === 0) {\n      if (currentStep < totalSteps) {\n        goToNextStep();\n      } else {\n        // Final submission\n        handleSubmit(e);\n      }\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div \n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n      onClick={handleBackdropClick}\n    >\n      <div \n        className=\"relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100 max-h-[90vh] overflow-hidden\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Automation Assessment\n            </h2>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n              Step {currentStep} of {totalSteps}\n            </p>\n          </div>\n          <button\n            onClick={handleClose}\n            disabled={isSubmitting}\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50\"\n          >\n            <X className=\"h-6 w-6 text-gray-500 dark:text-gray-400\" />\n          </button>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-800/50\">\n          <div className=\"flex items-center space-x-2\">\n            {Array.from({ length: totalSteps }, (_, i) => (\n              <div key={i} className=\"flex-1\">\n                <div className={`h-2 rounded-full transition-colors duration-300 ${\n                  i + 1 <= currentStep \n                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500' \n                    : 'bg-gray-200 dark:bg-gray-700'\n                }`} />\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[65vh]\">\n          {isSubmitted ? (\n            // Success State\n            <div className=\"text-center py-8\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-full mb-6\">\n                <CheckCircle className=\"h-8 w-8 text-blue-500\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                🚀 Your Automation Journey Starts Here!\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-6 leading-relaxed\">\n                Congratulations! You've taken the first step toward transforming your business with intelligent automation. \n                Based on your responses, we can see significant opportunities to save you time and reduce costs.\n              </p>\n              \n              <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6 mb-6\">\n                <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">What happens next:</h4>\n                <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span><strong>Within 24 hours:</strong> Personalized automation report in your inbox</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-cyan-500 rounded-full\"></div>\n                    <span><strong>Within 2 business days:</strong> Follow-up call to discuss your opportunities</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span><strong>Within 1 week:</strong> Custom automation proposal with fixed pricing</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-6\">\n                🇦🇺 Proudly Australian-owned • Over 40 years combined automation experience • Transparent pricing, local support, onshore data\n              </div>\n\n              <button\n                onClick={handleClose}\n                className=\"inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                Close\n              </button>\n            </div>\n          ) : (\n            // Form Steps\n            <form onSubmit={onFormSubmit} className=\"space-y-6\">\n              {/* Step 1: Business Assessment */}\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <div className=\"text-center mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                      Tell Us About Your Business\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      Understanding your current situation helps us identify the best automation opportunities\n                    </p>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Business Type *\n                      </label>\n                      <select\n                        name=\"businessType\"\n                        value={values.businessType}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        <option value=\"\">Select your industry...</option>\n                        <option value=\"professional-services\">Professional Services</option>\n                        <option value=\"retail-ecommerce\">Retail/E-commerce</option>\n                        <option value=\"manufacturing\">Manufacturing</option>\n                        <option value=\"healthcare\">Healthcare</option>\n                        <option value=\"real-estate\">Real Estate</option>\n                        <option value=\"hospitality\">Hospitality</option>\n                        <option value=\"other\">Other</option>\n                      </select>\n                      {errors.businessType && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.businessType}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Company Size *\n                      </label>\n                      <select\n                        name=\"companySize\"\n                        value={values.companySize}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        <option value=\"\">Select size...</option>\n                        <option value=\"just-me\">Just me (1 person)</option>\n                        <option value=\"small-team\">Small team (2-10 people)</option>\n                        <option value=\"growing-business\">Growing business (11-50 people)</option>\n                        <option value=\"established-company\">Established company (50+ people)</option>\n                      </select>\n                      {errors.companySize && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.companySize}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Current Biggest Challenge *\n                    </label>\n                    <select\n                      name=\"biggestChallenge\"\n                      value={values.biggestChallenge}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your biggest challenge...</option>\n                      <option value=\"manual-data-entry\">Too much manual data entry</option>\n                      <option value=\"admin-tasks\">Repetitive administrative tasks</option>\n                      <option value=\"system-communication\">Poor communication between systems</option>\n                      <option value=\"reporting\">Time-consuming reporting</option>\n                      <option value=\"customer-followup\">Customer follow-up processes</option>\n                      <option value=\"inventory-scheduling\">Inventory/scheduling management</option>\n                    </select>\n                    {errors.biggestChallenge && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.biggestChallenge}</p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Automation Opportunities */}\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <div className=\"text-center mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                      Where Do You Spend Most of Your Time?\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      Let's identify your biggest time drains that automation could eliminate\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Primary Time Waster *\n                    </label>\n                    <select\n                      name=\"primaryTimeWaster\"\n                      value={values.primaryTimeWaster}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your primary time waster...</option>\n                      <option value=\"administrative-tasks\">Administrative Tasks (invoicing, scheduling, file management)</option>\n                      <option value=\"customer-communications\">Customer Communications (follow-ups, reminders, support)</option>\n                      <option value=\"marketing-social\">Marketing & Social Media (content posting, lead nurturing)</option>\n                      <option value=\"data-entry-reporting\">Data Entry & Reporting (manual input, generating reports)</option>\n                      <option value=\"inventory-project\">Inventory/Project Management (tracking, updates, coordination)</option>\n                    </select>\n                    {errors.primaryTimeWaster && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.primaryTimeWaster}</p>\n                    )}\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Hours per week spent on this *\n                      </label>\n                      <div className=\"space-y-2\">\n                        <input\n                          type=\"range\"\n                          name=\"hoursPerWeek\"\n                          min=\"1\"\n                          max=\"40\"\n                          value={values.hoursPerWeek}\n                          onChange={handleChange}\n                          className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider\"\n                        />\n                        <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n                          <span>1 hour</span>\n                          <span className=\"font-medium text-blue-600 dark:text-blue-400\">{values.hoursPerWeek} hours</span>\n                          <span>40+ hours</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        How many people are involved? *\n                      </label>\n                      <select\n                        name=\"peopleInvolved\"\n                        value={values.peopleInvolved}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        required\n                      >\n                        {Array.from({ length: 10 }, (_, i) => (\n                          <option key={i + 1} value={i + 1}>\n                            {i + 1} {i === 0 ? 'person' : 'people'}\n                          </option>\n                        ))}\n                        <option value=\"10+\">10+ people</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Impact Estimation */}\n              {currentStep === 3 && (\n                <div className=\"space-y-6\">\n                  <div className=\"text-center mb-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      Let's Calculate Your Potential Savings\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      Based on your responses, here's what automation could save you\n                    </p>\n                  </div>\n\n                  <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6\">\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-4\">Time Investment Input:</h4>\n                    <div className=\"grid md:grid-cols-3 gap-4 mb-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Hours per week\n                        </label>\n                        <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                          {values.hoursPerWeek}\n                        </div>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Your hourly rate ($) *\n                        </label>\n                        <input\n                          type=\"number\"\n                          name=\"hourlyRate\"\n                          value={values.hourlyRate}\n                          onChange={handleChange}\n                          min=\"1\"\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                          required\n                        />\n                        {errors.hourlyRate && (\n                          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.hourlyRate}</p>\n                        )}\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Team members affected\n                        </label>\n                        <div className=\"text-2xl font-bold text-cyan-600 dark:text-cyan-400\">\n                          {values.peopleInvolved}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                      <h4 className=\"font-semibold text-gray-900 dark:text-white mb-4\">Potential Savings Preview:</h4>\n                      <div className=\"grid md:grid-cols-3 gap-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().weekly.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Weekly savings</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().monthly.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Monthly savings</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                            ${calculateSavings().annual.toLocaleString()}\n                          </div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">Annual savings</div>\n                        </div>\n                      </div>\n                      <div className=\"text-center mt-4 text-sm text-gray-600 dark:text-gray-400\">\n                        <strong>{calculateSavings().hoursPerWeek} hours per week</strong> could be automated\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Automation Confidence Level *\n                    </label>\n                    <select\n                      name=\"automationConfidence\"\n                      value={values.automationConfidence}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      required\n                    >\n                      <option value=\"\">Select your confidence level...</option>\n                      <option value=\"ready-now\">I'm ready to automate now</option>\n                      <option value=\"learn-more\">I want to learn more first</option>\n                      <option value=\"need-help\">I need help understanding the options</option>\n                    </select>\n                    {errors.automationConfidence && (\n                      <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.automationConfidence}</p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Contact Capture & Results */}\n              {currentStep === 4 && (\n                <div className=\"space-y-6\">\n                  <div className=\"text-center mb-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                      Get Your Personalized Automation Report\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      We'll send you a detailed analysis plus next steps for your automation journey\n                    </p>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email Address *\n                      </label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={values.email}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"<EMAIL>\"\n                        required\n                      />\n                      {errors.email && (\n                        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.email}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Phone Number\n                      </label>\n                      <input\n                        type=\"tel\"\n                        name=\"phone\"\n                        value={values.phone}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"+61 4XX XXX XXX\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Company Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        name=\"companyName\"\n                        value={values.companyName}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder=\"Your Company Pty Ltd\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Best time to contact\n                      </label>\n                      <select\n                        name=\"bestTimeToContact\"\n                        value={values.bestTimeToContact}\n                        onChange={handleChange}\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      >\n                        <option value=\"\">Select preferred time...</option>\n                        <option value=\"morning\">Morning (9am-12pm)</option>\n                        <option value=\"afternoon\">Afternoon (12pm-5pm)</option>\n                        <option value=\"evening\">Evening (5pm-7pm)</option>\n                        <option value=\"weekends\">Weekends</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/10 dark:to-blue-900/10 rounded-lg p-6\">\n                    <h4 className=\"font-semibold text-gray-900 dark:text-white mb-3\">Australian Value Promise:</h4>\n                    <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                        <span>Fixed-price automation quotes - no surprises</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span>Your data stays in Australian data centres</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                        <span>Free 30-minute strategy call with a senior automation engineer</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span>Serving businesses from Margaret River to Canberra</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Navigation Buttons */}\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700\">\n                <button\n                  type=\"button\"\n                  onClick={goToPreviousStep}\n                  disabled={currentStep === 1 || isSubmitting}\n                  className={`inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors duration-200 ${\n                    currentStep === 1 || isSubmitting\n                      ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Previous\n                </button>\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Processing...\n                    </>\n                  ) : currentStep === totalSteps ? (\n                    <>\n                      <Calculator className=\"h-4 w-4 mr-2\" />\n                      Get My Report\n                    </>\n                  ) : (\n                    <>\n                      Next Step\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AutomationAssessmentModal;\n"}