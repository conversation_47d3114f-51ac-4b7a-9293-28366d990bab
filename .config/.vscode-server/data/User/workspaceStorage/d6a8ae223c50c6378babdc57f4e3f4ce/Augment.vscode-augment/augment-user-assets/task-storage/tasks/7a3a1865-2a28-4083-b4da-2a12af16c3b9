{"uuid": "7a3a1865-2a28-4083-b4da-2a12af16c3b9", "name": "Automation assesment", "description": "when I click on the calculate my automation savings button I want the page that shows up to not need to scroll. It looks like maybe a better visual way to have the list instead of radio buttons a sort of drop down menu that allows you to select multiple options would be good. Conserve real estate and make sure the size of the modal box is standadrised between the quick quote and the automation assesment i want everything to feel unified and consistent across the whole website expeirence.", "state": "COMPLETE", "subTasks": [], "lastUpdated": 1749894267253, "lastUpdatedBy": "AGENT"}