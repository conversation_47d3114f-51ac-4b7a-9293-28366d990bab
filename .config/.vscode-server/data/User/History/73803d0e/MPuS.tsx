import React from 'react';
import { Globe } from 'lucide-react';

const TechStack: React.FC = () => {

  return (
    <section id="tech" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Our <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Technologies</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            We leverage cutting-edge technologies to build scalable, performant, and future-ready solutions for Australian businesses
          </p>
        </div>



        {/* Bottom Section */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full px-6 py-3 mb-6">
            <Globe className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            <span className="text-gray-700 dark:text-gray-300 font-medium">Always Learning, Always Innovating</span>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Our team stays at the forefront of technology, continuously learning and adopting new tools to deliver the best solutions for our clients.
          </p>
        </div>
      </div>
    </section>
  );
};

export default TechStack;