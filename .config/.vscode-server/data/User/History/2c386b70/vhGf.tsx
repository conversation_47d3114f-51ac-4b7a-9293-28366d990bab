import React, { useState } from 'react';
import { X, <PERSON><PERSON><PERSON><PERSON>, Calculator, ArrowRight, ArrowLeft, Loader2 } from 'lucide-react';
import { useForm } from '../hooks/useForm';
import { validateAutomationAssessmentForm, type AutomationAssessmentFormData } from '../utils/validation';
import { submitAutomationAssessmentForm } from '../utils/emailService';

interface AutomationAssessmentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AutomationAssessmentModal: React.FC<AutomationAssessmentModalProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const {
    values,
    errors,
    isSubmitting,
    isSubmitted,
    submitMessage,
    handleChange,
    handleSubmit,
    setFieldValue,
    resetForm
  } = useForm<AutomationAssessmentFormData>({
    initialValues: {
      businessType: '',
      companySize: '',
      biggestChallenge: '',
      primaryTimeWaster: '',
      hoursPerWeek: 5,
      peopleInvolved: 1,
      hourlyRate: 50,
      automationConfidence: '',
      email: '',
      phone: '',
      companyName: '',
      bestTimeToContact: ''
    },
    validate: (values) => validateAutomationAssessmentForm(values, currentStep).errors,
    onSubmit: async (values) => {
      const result = await submitAutomationAssessmentForm(values);
      return result;
    }
  });

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setTimeout(() => {
        setCurrentStep(1);
        resetForm();
      }, 300);
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Calculate potential savings
  const calculateSavings = () => {
    const weeklyHours = values.hoursPerWeek;
    const hourlyRate = values.hourlyRate;
    const people = values.peopleInvolved;
    
    const weeklySavings = weeklyHours * hourlyRate * people * 0.7; // 70% automation efficiency
    const monthlySavings = weeklySavings * 4.33;
    const annualSavings = monthlySavings * 12;
    
    return {
      weekly: Math.round(weeklySavings),
      monthly: Math.round(monthlySavings),
      annual: Math.round(annualSavings),
      hoursPerWeek: Math.round(weeklyHours * people * 0.7)
    };
  };

  // Form submission handler
  const onFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate current step
    const stepErrors = validateAutomationAssessmentForm(values, currentStep).errors;
    
    if (Object.keys(stepErrors).length === 0) {
      if (currentStep < totalSteps) {
        goToNextStep();
      } else {
        // Final submission
        handleSubmit(e);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div 
        className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100 max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Automation Assessment
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Step {currentStep} of {totalSteps}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 disabled:opacity-50"
          >
            <X className="h-6 w-6 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center space-x-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div key={i} className="flex-1">
                <div className={`h-2 rounded-full transition-colors duration-300 ${
                  i + 1 <= currentStep 
                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500' 
                    : 'bg-gray-200 dark:bg-gray-700'
                }`} />
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[65vh]">
          {isSubmitted ? (
            // Success State
            <div className="text-center py-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-full mb-6">
                <CheckCircle className="h-8 w-8 text-blue-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                🚀 Your Automation Journey Starts Here!
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                Congratulations! You've taken the first step toward transforming your business with intelligent automation. 
                Based on your responses, we can see significant opportunities to save you time and reduce costs.
              </p>
              
              <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6 mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-3">What happens next:</h4>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span><strong>Within 24 hours:</strong> Personalized automation report in your inbox</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
                    <span><strong>Within 2 business days:</strong> Follow-up call to discuss your opportunities</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span><strong>Within 1 week:</strong> Custom automation proposal with fixed pricing</span>
                  </div>
                </div>
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-400 mb-6">
                🇦🇺 Proudly Australian-owned • Over 40 years combined automation experience • Transparent pricing, local support, onshore data
              </div>

              <button
                onClick={handleClose}
                className="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200"
              >
                Close
              </button>
            </div>
          ) : (
            // Form Steps
            <form onSubmit={onFormSubmit} className="space-y-6">
              {/* Step 1: Business Assessment */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Tell Us About Your Business
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Understanding your current situation helps us identify the best automation opportunities
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Business Type *
                      </label>
                      <select
                        name="businessType"
                        value={values.businessType}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select your industry...</option>
                        <option value="professional-services">Professional Services</option>
                        <option value="retail-ecommerce">Retail/E-commerce</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="real-estate">Real Estate</option>
                        <option value="hospitality">Hospitality</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.businessType && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.businessType}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company Size *
                      </label>
                      <select
                        name="companySize"
                        value={values.companySize}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        <option value="">Select size...</option>
                        <option value="just-me">Just me (1 person)</option>
                        <option value="small-team">Small team (2-10 people)</option>
                        <option value="growing-business">Growing business (11-50 people)</option>
                        <option value="established-company">Established company (50+ people)</option>
                      </select>
                      {errors.companySize && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.companySize}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Current Biggest Challenge *
                    </label>
                    <select
                      name="biggestChallenge"
                      value={values.biggestChallenge}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      required
                    >
                      <option value="">Select your biggest challenge...</option>
                      <option value="manual-data-entry">Too much manual data entry</option>
                      <option value="admin-tasks">Repetitive administrative tasks</option>
                      <option value="system-communication">Poor communication between systems</option>
                      <option value="reporting">Time-consuming reporting</option>
                      <option value="customer-followup">Customer follow-up processes</option>
                      <option value="inventory-scheduling">Inventory/scheduling management</option>
                    </select>
                    {errors.biggestChallenge && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.biggestChallenge}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Step 2: Automation Opportunities */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Where Do You Spend Most of Your Time?
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Let's identify your biggest time drains that automation could eliminate
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Primary Time Waster *
                    </label>
                    <select
                      name="primaryTimeWaster"
                      value={values.primaryTimeWaster}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      required
                    >
                      <option value="">Select your primary time waster...</option>
                      <option value="administrative-tasks">Administrative Tasks (invoicing, scheduling, file management)</option>
                      <option value="customer-communications">Customer Communications (follow-ups, reminders, support)</option>
                      <option value="marketing-social">Marketing & Social Media (content posting, lead nurturing)</option>
                      <option value="data-entry-reporting">Data Entry & Reporting (manual input, generating reports)</option>
                      <option value="inventory-project">Inventory/Project Management (tracking, updates, coordination)</option>
                    </select>
                    {errors.primaryTimeWaster && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.primaryTimeWaster}</p>
                    )}
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Hours per week spent on this *
                      </label>
                      <div className="space-y-2">
                        <input
                          type="range"
                          name="hoursPerWeek"
                          min="1"
                          max="40"
                          value={values.hoursPerWeek}
                          onChange={handleChange}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
                        />
                        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                          <span>1 hour</span>
                          <span className="font-medium text-blue-600 dark:text-blue-400">{values.hoursPerWeek} hours</span>
                          <span>40+ hours</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        How many people are involved? *
                      </label>
                      <select
                        name="peopleInvolved"
                        value={values.peopleInvolved}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      >
                        {Array.from({ length: 10 }, (_, i) => (
                          <option key={i + 1} value={i + 1}>
                            {i + 1} {i === 0 ? 'person' : 'people'}
                          </option>
                        ))}
                        <option value="10+">10+ people</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Impact Estimation */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Let's Calculate Your Potential Savings
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Based on your responses, here's what automation could save you
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Time Investment Input:</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Hours per week
                        </label>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {values.hoursPerWeek}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Your hourly rate ($) *
                        </label>
                        <input
                          type="number"
                          name="hourlyRate"
                          value={values.hourlyRate}
                          onChange={handleChange}
                          min="1"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          required
                        />
                        {errors.hourlyRate && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.hourlyRate}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Team members affected
                        </label>
                        <div className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                          {values.peopleInvolved}
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Potential Savings Preview:</h4>
                      <div className="grid md:grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            ${calculateSavings().weekly.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Weekly savings</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            ${calculateSavings().monthly.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Monthly savings</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            ${calculateSavings().annual.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Annual savings</div>
                        </div>
                      </div>
                      <div className="text-center mt-4 text-sm text-gray-600 dark:text-gray-400">
                        <strong>{calculateSavings().hoursPerWeek} hours per week</strong> could be automated
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Automation Confidence Level *
                    </label>
                    <select
                      name="automationConfidence"
                      value={values.automationConfidence}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      required
                    >
                      <option value="">Select your confidence level...</option>
                      <option value="ready-now">I'm ready to automate now</option>
                      <option value="learn-more">I want to learn more first</option>
                      <option value="need-help">I need help understanding the options</option>
                    </select>
                    {errors.automationConfidence && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.automationConfidence}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Step 4: Contact Capture & Results */}
              {currentStep === 4 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Get Your Personalized Automation Report
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      We'll send you a detailed analysis plus next steps for your automation journey
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={values.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="<EMAIL>"
                        required
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={values.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="+61 4XX XXX XXX"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Company Name
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={values.companyName}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Your Company Pty Ltd"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Best time to contact
                      </label>
                      <select
                        name="bestTimeToContact"
                        value={values.bestTimeToContact}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Select preferred time...</option>
                        <option value="morning">Morning (9am-12pm)</option>
                        <option value="afternoon">Afternoon (12pm-5pm)</option>
                        <option value="evening">Evening (5pm-7pm)</option>
                        <option value="weekends">Weekends</option>
                      </select>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/10 dark:to-blue-900/10 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Australian Value Promise:</h4>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Fixed-price automation quotes - no surprises</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Your data stays in Australian data centres</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Free 30-minute strategy call with a senior automation engineer</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Serving businesses from Margaret River to Canberra</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={goToPreviousStep}
                  disabled={currentStep === 1 || isSubmitting}
                  className={`inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors duration-200 ${
                    currentStep === 1 || isSubmitting
                      ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </button>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : currentStep === totalSteps ? (
                    <>
                      <Calculator className="h-4 w-4 mr-2" />
                      Get My Report
                    </>
                  ) : (
                    <>
                      Next Step
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default AutomationAssessmentModal;
