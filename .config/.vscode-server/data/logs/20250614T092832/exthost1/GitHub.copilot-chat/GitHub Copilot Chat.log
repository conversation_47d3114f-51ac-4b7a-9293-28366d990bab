2025-06-14 09:28:47.204 [info] Can't use the Electron fetcher in this environment.
2025-06-14 09:28:47.204 [info] Using the Node fetch fetcher.
2025-06-14 09:28:47.204 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-14 09:28:47.204 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-14 09:28:47.204 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-14 09:28:47.204 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-14 09:28:48.741 [info] Logged in as edwardbowman_nbnco
2025-06-14 09:28:49.675 [info] Got Copilot token for edwardbowman_nbnco
2025-06-14 09:28:49.680 [info] activationBlocker from 'languageModelAccess' took for 3641ms
2025-06-14 09:28:50.341 [info] Fetched model metadata in 661ms e2659b59-6d06-4238-9ba8-2cea22ac720b
2025-06-14 09:28:51.147 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-14 09:28:51.162 [info] Registering default platform agent...
2025-06-14 09:28:51.162 [info] activationBlocker from 'conversationFeature' took for 5128ms
2025-06-14 09:28:51.163 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-14 09:28:51.163 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-14 09:28:51.163 [info] Successfully registered GitHub PR title and description provider.
2025-06-14 09:28:51.163 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-14 09:28:52.069 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-14 09:28:52.321 [info] Fetched content exclusion rules in 925ms
2025-06-14 09:28:53.411 [info] Fetched content exclusion rules in 1090ms
2025-06-14 09:28:53.419 [info] Fetched content exclusion rules in 1098ms
