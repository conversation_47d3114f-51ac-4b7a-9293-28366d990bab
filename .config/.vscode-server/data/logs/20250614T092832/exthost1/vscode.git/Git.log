2025-06-14 09:28:45.423 [info] [main] Log level: Info
2025-06-14 09:28:45.423 [info] [main] Validating found git in: "git"
2025-06-14 09:28:45.423 [info] [main] Using git "2.47.2" from "git"
2025-06-14 09:28:45.423 [info] [Model][doInitialScan] Initial repository scan started
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-14 09:28:45.423 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-14 09:28:45.423 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-14 09:28:45.423 [info] > git config --get commit.template [4ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [3ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [2ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [2ms]
2025-06-14 09:28:45.423 [info] > git status -z -uall [25ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [12ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [22ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-14 09:28:45.423 [info] > git config --get commit.template [16ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [20ms]
2025-06-14 09:28:45.423 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [6ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [67ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [49ms]
2025-06-14 09:28:45.423 [info] > git merge-base refs/heads/main refs/remotes/origin/main [60ms]
2025-06-14 09:28:45.423 [info] > git diff --name-status -z --diff-filter=ADMR b513a640b2eff60033b3d94570986b2e462015a9...refs/remotes/origin/main [8ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [11ms]
2025-06-14 09:28:45.423 [info] > git status -z -uall [11ms]
2025-06-14 09:28:45.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-14 09:28:45.423 [info] > git rev-parse --show-toplevel [2274ms]
2025-06-14 09:28:45.464 [info] > git check-ignore -v -z --stdin [42ms]
2025-06-14 09:28:45.465 [info] > git rev-parse --show-toplevel [39ms]
2025-06-14 09:28:45.468 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-14 09:28:46.228 [info] > git check-ignore -v -z --stdin [161ms]
2025-06-14 09:28:47.099 [info] > git show --textconv :.config/.vscode-server/data/User/History/6983ec26/GlEu.html [564ms]
2025-06-14 09:28:47.099 [info] > git ls-files --stage -- .config/.vscode-server/data/User/History/6983ec26/GlEu.html [561ms]
2025-06-14 09:28:47.191 [info] > git hash-object -t tree /dev/null [85ms]
2025-06-14 09:28:47.192 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/workspace/.config/.vscode-server/data/User/History/6983ec26/GlEu.html.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.config%2F.vscode-server%2Fdata%2FUser%2FHistory%2F6983ec26%2FGlEu.html%22%2C%22ref%22%3A%22%22%7D
2025-06-14 09:28:47.195 [info] > git hash-object -t tree /dev/null [92ms]
2025-06-14 09:28:47.195 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/workspace/.config/.vscode-server/data/User/History/6983ec26/GlEu.html.git?%7B%22path%22%3A%22%2Fhome%2Frunner%2Fworkspace%2F.config%2F.vscode-server%2Fdata%2FUser%2FHistory%2F6983ec26%2FGlEu.html%22%2C%22ref%22%3A%22%22%7D
2025-06-14 09:28:47.921 [info] > git config --get commit.template [11ms]
2025-06-14 09:28:47.923 [info] > git blame --root --incremental 81f011c574c3a92bfc24d158c2cf936416a872c7 -- .config/.vscode-server/data/User/History/6983ec26/GlEu.html [4ms]
2025-06-14 09:28:47.923 [info] fatal: no such path .config/.vscode-server/data/User/History/6983ec26/GlEu.html in 81f011c574c3a92bfc24d158c2cf936416a872c7
2025-06-14 09:28:47.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:28:47.942 [info] > git status -z -uall [8ms]
2025-06-14 09:28:47.943 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-14 09:28:49.250 [info] > git config --get --local branch.main.github-pr-owner-number [123ms]
2025-06-14 09:28:49.250 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 09:29:11.103 [info] > git config --get commit.template [5ms]
2025-06-14 09:29:11.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:11.112 [info] > git status -z -uall [3ms]
2025-06-14 09:29:11.113 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:29:11.302 [info] > git fetch [211ms]
2025-06-14 09:29:11.302 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-14 09:29:11.313 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:29:11.313 [info] > git config --get commit.template [6ms]
2025-06-14 09:29:11.325 [info] > git status -z -uall [4ms]
2025-06-14 09:29:11.327 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:29:30.147 [info] > git config --get commit.template [3ms]
2025-06-14 09:29:30.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:30.157 [info] > git status -z -uall [4ms]
2025-06-14 09:29:30.158 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:29:35.170 [info] > git config --get commit.template [4ms]
2025-06-14 09:29:35.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:35.179 [info] > git status -z -uall [4ms]
2025-06-14 09:29:35.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-14 09:29:40.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:40.198 [info] > git config --get commit.template [5ms]
2025-06-14 09:29:40.204 [info] > git status -z -uall [3ms]
2025-06-14 09:29:40.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:29:45.218 [info] > git config --get commit.template [5ms]
2025-06-14 09:29:45.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:45.228 [info] > git status -z -uall [5ms]
2025-06-14 09:29:45.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:29:50.240 [info] > git config --get commit.template [2ms]
2025-06-14 09:29:50.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:29:50.253 [info] > git status -z -uall [3ms]
2025-06-14 09:29:50.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:29:55.269 [info] > git config --get commit.template [6ms]
2025-06-14 09:29:55.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:29:55.280 [info] > git status -z -uall [5ms]
2025-06-14 09:29:55.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:00.294 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:00.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:00.302 [info] > git status -z -uall [4ms]
2025-06-14 09:30:00.303 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:05.313 [info] > git config --get commit.template [3ms]
2025-06-14 09:30:05.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:05.323 [info] > git status -z -uall [5ms]
2025-06-14 09:30:05.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:10.334 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:10.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:30:10.343 [info] > git status -z -uall [3ms]
2025-06-14 09:30:10.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:15.359 [info] > git config --get commit.template [6ms]
2025-06-14 09:30:15.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:15.366 [info] > git status -z -uall [4ms]
2025-06-14 09:30:15.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:20.385 [info] > git config --get commit.template [7ms]
2025-06-14 09:30:20.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 09:30:20.403 [info] > git status -z -uall [8ms]
2025-06-14 09:30:20.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:25.415 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:25.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:30:25.425 [info] > git status -z -uall [4ms]
2025-06-14 09:30:25.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:30.436 [info] > git config --get commit.template [1ms]
2025-06-14 09:30:30.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:30.453 [info] > git status -z -uall [6ms]
2025-06-14 09:30:30.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:35.464 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:35.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:35.473 [info] > git status -z -uall [4ms]
2025-06-14 09:30:35.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:40.484 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:40.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:30:40.495 [info] > git status -z -uall [6ms]
2025-06-14 09:30:40.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:45.509 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:45.510 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:45.523 [info] > git status -z -uall [5ms]
2025-06-14 09:30:45.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:50.537 [info] > git config --get commit.template [3ms]
2025-06-14 09:30:50.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:30:50.544 [info] > git status -z -uall [3ms]
2025-06-14 09:30:50.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:30:55.558 [info] > git config --get commit.template [4ms]
2025-06-14 09:30:55.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:30:55.567 [info] > git status -z -uall [4ms]
2025-06-14 09:30:55.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:00.579 [info] > git config --get commit.template [4ms]
2025-06-14 09:31:00.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:31:00.587 [info] > git status -z -uall [3ms]
2025-06-14 09:31:00.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:05.598 [info] > git config --get commit.template [3ms]
2025-06-14 09:31:05.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:05.606 [info] > git status -z -uall [3ms]
2025-06-14 09:31:05.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:10.618 [info] > git config --get commit.template [4ms]
2025-06-14 09:31:10.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:10.626 [info] > git status -z -uall [3ms]
2025-06-14 09:31:10.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:15.638 [info] > git config --get commit.template [4ms]
2025-06-14 09:31:15.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:31:15.644 [info] > git status -z -uall [3ms]
2025-06-14 09:31:15.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:31:20.659 [info] > git config --get commit.template [5ms]
2025-06-14 09:31:20.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:20.666 [info] > git status -z -uall [3ms]
2025-06-14 09:31:20.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:31.151 [info] > git config --get commit.template [4ms]
2025-06-14 09:31:31.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:31.161 [info] > git status -z -uall [5ms]
2025-06-14 09:31:31.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:31:36.170 [info] > git config --get commit.template [3ms]
2025-06-14 09:31:36.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:36.179 [info] > git status -z -uall [4ms]
2025-06-14 09:31:36.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:31:49.370 [info] > git config --get commit.template [3ms]
2025-06-14 09:31:49.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:49.379 [info] > git status -z -uall [4ms]
2025-06-14 09:31:49.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:54.389 [info] > git config --get commit.template [3ms]
2025-06-14 09:31:54.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:31:54.397 [info] > git status -z -uall [3ms]
2025-06-14 09:31:54.398 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:31:59.409 [info] > git config --get commit.template [4ms]
2025-06-14 09:31:59.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:31:59.417 [info] > git status -z -uall [3ms]
2025-06-14 09:31:59.420 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:32:04.431 [info] > git config --get commit.template [4ms]
2025-06-14 09:32:04.432 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:04.438 [info] > git status -z -uall [3ms]
2025-06-14 09:32:04.439 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:32:09.451 [info] > git config --get commit.template [5ms]
2025-06-14 09:32:09.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:09.463 [info] > git status -z -uall [6ms]
2025-06-14 09:32:09.465 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:32:14.475 [info] > git config --get commit.template [2ms]
2025-06-14 09:32:14.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:32:14.488 [info] > git status -z -uall [3ms]
2025-06-14 09:32:14.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:32:19.500 [info] > git config --get commit.template [2ms]
2025-06-14 09:32:19.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:19.523 [info] > git status -z -uall [9ms]
2025-06-14 09:32:19.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:32:24.535 [info] > git config --get commit.template [4ms]
2025-06-14 09:32:24.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:24.543 [info] > git status -z -uall [4ms]
2025-06-14 09:32:24.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:32:29.552 [info] > git config --get commit.template [1ms]
2025-06-14 09:32:29.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:29.564 [info] > git status -z -uall [4ms]
2025-06-14 09:32:29.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:32:34.578 [info] > git config --get commit.template [5ms]
2025-06-14 09:32:34.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:32:34.593 [info] > git status -z -uall [6ms]
2025-06-14 09:32:34.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:32:39.602 [info] > git config --get commit.template [2ms]
2025-06-14 09:32:39.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:32:39.616 [info] > git status -z -uall [4ms]
2025-06-14 09:32:39.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:32:44.635 [info] > git config --get commit.template [8ms]
2025-06-14 09:32:44.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:44.646 [info] > git status -z -uall [6ms]
2025-06-14 09:32:44.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 09:32:49.659 [info] > git config --get commit.template [4ms]
2025-06-14 09:32:49.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:49.667 [info] > git status -z -uall [4ms]
2025-06-14 09:32:49.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:32:54.678 [info] > git config --get commit.template [4ms]
2025-06-14 09:32:54.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:32:54.686 [info] > git status -z -uall [3ms]
2025-06-14 09:32:54.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:32:59.700 [info] > git config --get commit.template [5ms]
2025-06-14 09:32:59.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:32:59.710 [info] > git status -z -uall [5ms]
2025-06-14 09:32:59.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:04.726 [info] > git config --get commit.template [6ms]
2025-06-14 09:33:04.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:04.735 [info] > git status -z -uall [3ms]
2025-06-14 09:33:04.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:09.755 [info] > git config --get commit.template [7ms]
2025-06-14 09:33:09.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:33:09.775 [info] > git status -z -uall [11ms]
2025-06-14 09:33:09.776 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:33:14.792 [info] > git config --get commit.template [6ms]
2025-06-14 09:33:14.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:14.826 [info] > git status -z -uall [28ms]
2025-06-14 09:33:14.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-14 09:33:19.841 [info] > git config --get commit.template [4ms]
2025-06-14 09:33:19.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:19.853 [info] > git status -z -uall [5ms]
2025-06-14 09:33:19.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:24.865 [info] > git config --get commit.template [4ms]
2025-06-14 09:33:24.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:33:24.873 [info] > git status -z -uall [4ms]
2025-06-14 09:33:24.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:29.885 [info] > git config --get commit.template [5ms]
2025-06-14 09:33:29.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:29.896 [info] > git status -z -uall [5ms]
2025-06-14 09:33:29.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:35.557 [info] > git config --get commit.template [5ms]
2025-06-14 09:33:35.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:35.566 [info] > git status -z -uall [4ms]
2025-06-14 09:33:35.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:40.579 [info] > git config --get commit.template [4ms]
2025-06-14 09:33:40.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:33:40.587 [info] > git status -z -uall [3ms]
2025-06-14 09:33:40.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:33:45.602 [info] > git config --get commit.template [6ms]
2025-06-14 09:33:45.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:45.613 [info] > git status -z -uall [4ms]
2025-06-14 09:33:45.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:33:50.871 [info] > git config --get commit.template [3ms]
2025-06-14 09:33:50.873 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:33:50.882 [info] > git status -z -uall [5ms]
2025-06-14 09:33:50.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:33:55.898 [info] > git config --get commit.template [6ms]
2025-06-14 09:33:55.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-14 09:33:55.921 [info] > git status -z -uall [8ms]
2025-06-14 09:33:55.923 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:34:09.593 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:09.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:09.603 [info] > git status -z -uall [5ms]
2025-06-14 09:34:09.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:34:14.624 [info] > git config --get commit.template [9ms]
2025-06-14 09:34:14.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:34:14.633 [info] > git status -z -uall [4ms]
2025-06-14 09:34:14.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:34:19.646 [info] > git config --get commit.template [3ms]
2025-06-14 09:34:19.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:34:19.656 [info] > git status -z -uall [4ms]
2025-06-14 09:34:19.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:34:29.880 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:29.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:29.890 [info] > git status -z -uall [5ms]
2025-06-14 09:34:29.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:34:34.904 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:34.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:34.913 [info] > git status -z -uall [4ms]
2025-06-14 09:34:34.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:34:39.925 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:39.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:39.932 [info] > git status -z -uall [3ms]
2025-06-14 09:34:39.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:34:44.945 [info] > git config --get commit.template [0ms]
2025-06-14 09:34:44.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:44.963 [info] > git status -z -uall [5ms]
2025-06-14 09:34:44.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:34:49.977 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:49.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:49.986 [info] > git status -z -uall [4ms]
2025-06-14 09:34:49.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:34:54.998 [info] > git config --get commit.template [4ms]
2025-06-14 09:34:54.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:34:55.005 [info] > git status -z -uall [3ms]
2025-06-14 09:34:55.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:35:00.017 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:00.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:35:00.025 [info] > git status -z -uall [4ms]
2025-06-14 09:35:00.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:35:05.038 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:05.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:35:05.049 [info] > git status -z -uall [4ms]
2025-06-14 09:35:05.050 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:35:10.062 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:10.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:35:10.070 [info] > git status -z -uall [4ms]
2025-06-14 09:35:10.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:35:15.107 [info] > git config --get commit.template [26ms]
2025-06-14 09:35:15.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-14 09:35:15.130 [info] > git status -z -uall [8ms]
2025-06-14 09:35:15.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:35:21.898 [info] > git config --get commit.template [2ms]
2025-06-14 09:35:21.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:35:21.918 [info] > git status -z -uall [7ms]
2025-06-14 09:35:21.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:35:26.932 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:26.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:35:26.940 [info] > git status -z -uall [3ms]
2025-06-14 09:35:26.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:35:41.900 [info] > git config --get commit.template [1ms]
2025-06-14 09:35:41.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:35:41.921 [info] > git status -z -uall [6ms]
2025-06-14 09:35:41.925 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 09:35:46.938 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:46.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:35:46.946 [info] > git status -z -uall [3ms]
2025-06-14 09:35:46.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:35:51.958 [info] > git config --get commit.template [4ms]
2025-06-14 09:35:51.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:35:51.971 [info] > git status -z -uall [7ms]
2025-06-14 09:35:51.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:36:17.820 [info] > git config --get commit.template [2ms]
2025-06-14 09:36:17.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:36:17.833 [info] > git status -z -uall [3ms]
2025-06-14 09:36:17.834 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:36:22.859 [info] > git config --get commit.template [5ms]
2025-06-14 09:36:22.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:36:22.868 [info] > git status -z -uall [4ms]
2025-06-14 09:36:22.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:36:27.879 [info] > git config --get commit.template [4ms]
2025-06-14 09:36:27.880 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:36:27.887 [info] > git status -z -uall [4ms]
2025-06-14 09:36:27.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:36:32.899 [info] > git config --get commit.template [4ms]
2025-06-14 09:36:32.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:36:32.908 [info] > git status -z -uall [5ms]
2025-06-14 09:36:32.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:36:37.921 [info] > git config --get commit.template [4ms]
2025-06-14 09:36:37.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:36:37.929 [info] > git status -z -uall [3ms]
2025-06-14 09:36:37.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:36:42.941 [info] > git config --get commit.template [4ms]
2025-06-14 09:36:42.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:36:42.949 [info] > git status -z -uall [3ms]
2025-06-14 09:36:42.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:37:18.232 [info] > git config --get commit.template [5ms]
2025-06-14 09:37:18.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:18.239 [info] > git status -z -uall [4ms]
2025-06-14 09:37:18.240 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:37:23.256 [info] > git config --get commit.template [4ms]
2025-06-14 09:37:23.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:37:23.268 [info] > git status -z -uall [5ms]
2025-06-14 09:37:23.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:37:28.283 [info] > git config --get commit.template [5ms]
2025-06-14 09:37:28.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:28.293 [info] > git status -z -uall [5ms]
2025-06-14 09:37:28.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:37:33.306 [info] > git config --get commit.template [5ms]
2025-06-14 09:37:33.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:33.315 [info] > git status -z -uall [4ms]
2025-06-14 09:37:33.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:37:38.330 [info] > git config --get commit.template [4ms]
2025-06-14 09:37:38.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:38.340 [info] > git status -z -uall [4ms]
2025-06-14 09:37:38.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:37:43.358 [info] > git config --get commit.template [4ms]
2025-06-14 09:37:43.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:43.368 [info] > git status -z -uall [5ms]
2025-06-14 09:37:43.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:37:48.388 [info] > git config --get commit.template [2ms]
2025-06-14 09:37:48.402 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:37:48.413 [info] > git status -z -uall [5ms]
2025-06-14 09:37:48.414 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:37:53.427 [info] > git config --get commit.template [5ms]
2025-06-14 09:37:53.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:37:53.435 [info] > git status -z -uall [3ms]
2025-06-14 09:37:53.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:37:58.452 [info] > git config --get commit.template [7ms]
2025-06-14 09:37:58.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:37:58.461 [info] > git status -z -uall [4ms]
2025-06-14 09:37:58.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:38:03.485 [info] > git config --get commit.template [11ms]
2025-06-14 09:38:03.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 09:38:03.509 [info] > git status -z -uall [11ms]
2025-06-14 09:38:03.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:38:08.530 [info] > git config --get commit.template [6ms]
2025-06-14 09:38:08.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:38:08.541 [info] > git status -z -uall [4ms]
2025-06-14 09:38:08.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:38:13.553 [info] > git config --get commit.template [4ms]
2025-06-14 09:38:13.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:38:13.563 [info] > git status -z -uall [5ms]
2025-06-14 09:38:13.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:38:18.585 [info] > git config --get commit.template [7ms]
2025-06-14 09:38:18.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:38:18.597 [info] > git status -z -uall [6ms]
2025-06-14 09:38:18.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:38:23.612 [info] > git config --get commit.template [5ms]
2025-06-14 09:38:23.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:38:23.623 [info] > git status -z -uall [7ms]
2025-06-14 09:38:23.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:38:28.633 [info] > git config --get commit.template [1ms]
2025-06-14 09:38:28.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:38:28.644 [info] > git status -z -uall [4ms]
2025-06-14 09:38:28.645 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:38:33.655 [info] > git config --get commit.template [4ms]
2025-06-14 09:38:33.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:38:33.665 [info] > git status -z -uall [4ms]
2025-06-14 09:38:33.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:38:38.673 [info] > git config --get commit.template [2ms]
2025-06-14 09:38:38.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:38:38.685 [info] > git status -z -uall [3ms]
2025-06-14 09:38:38.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:38:43.698 [info] > git config --get commit.template [4ms]
2025-06-14 09:38:43.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:38:43.706 [info] > git status -z -uall [4ms]
2025-06-14 09:38:43.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:38:48.719 [info] > git config --get commit.template [4ms]
2025-06-14 09:38:48.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:38:48.728 [info] > git status -z -uall [4ms]
2025-06-14 09:38:48.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:38:53.939 [info] > git config --get commit.template [4ms]
2025-06-14 09:38:53.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:38:53.949 [info] > git status -z -uall [4ms]
2025-06-14 09:38:53.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:38:58.958 [info] > git config --get commit.template [2ms]
2025-06-14 09:38:58.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:38:58.970 [info] > git status -z -uall [3ms]
2025-06-14 09:38:58.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:03.984 [info] > git config --get commit.template [4ms]
2025-06-14 09:39:03.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:03.994 [info] > git status -z -uall [5ms]
2025-06-14 09:39:03.994 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:09.008 [info] > git config --get commit.template [5ms]
2025-06-14 09:39:09.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:39:09.019 [info] > git status -z -uall [6ms]
2025-06-14 09:39:09.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:14.032 [info] > git config --get commit.template [4ms]
2025-06-14 09:39:14.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:14.039 [info] > git status -z -uall [3ms]
2025-06-14 09:39:14.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:19.052 [info] > git config --get commit.template [4ms]
2025-06-14 09:39:19.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:19.063 [info] > git status -z -uall [5ms]
2025-06-14 09:39:19.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:39:24.075 [info] > git config --get commit.template [3ms]
2025-06-14 09:39:24.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:39:24.085 [info] > git status -z -uall [5ms]
2025-06-14 09:39:24.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:39:29.095 [info] > git config --get commit.template [4ms]
2025-06-14 09:39:29.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:29.103 [info] > git status -z -uall [4ms]
2025-06-14 09:39:29.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:34.121 [info] > git config --get commit.template [6ms]
2025-06-14 09:39:34.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:39:34.137 [info] > git status -z -uall [6ms]
2025-06-14 09:39:34.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-14 09:39:39.157 [info] > git config --get commit.template [7ms]
2025-06-14 09:39:39.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:39.182 [info] > git status -z -uall [9ms]
2025-06-14 09:39:39.183 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:39:44.197 [info] > git config --get commit.template [5ms]
2025-06-14 09:39:44.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:44.212 [info] > git status -z -uall [4ms]
2025-06-14 09:39:44.213 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:49.221 [info] > git config --get commit.template [1ms]
2025-06-14 09:39:49.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:39:49.233 [info] > git status -z -uall [3ms]
2025-06-14 09:39:49.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:39:54.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-14 09:39:54.249 [info] > git config --get commit.template [7ms]
2025-06-14 09:39:54.259 [info] > git status -z -uall [6ms]
2025-06-14 09:39:54.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:39:59.272 [info] > git config --get commit.template [4ms]
2025-06-14 09:39:59.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:39:59.282 [info] > git status -z -uall [4ms]
2025-06-14 09:39:59.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:04.295 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:04.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:04.303 [info] > git status -z -uall [4ms]
2025-06-14 09:40:04.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:40:09.316 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:09.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:09.323 [info] > git status -z -uall [3ms]
2025-06-14 09:40:09.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:14.336 [info] > git config --get commit.template [5ms]
2025-06-14 09:40:14.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:14.345 [info] > git status -z -uall [3ms]
2025-06-14 09:40:14.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:40:19.357 [info] > git config --get commit.template [3ms]
2025-06-14 09:40:19.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:19.366 [info] > git status -z -uall [3ms]
2025-06-14 09:40:19.368 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:24.380 [info] > git config --get commit.template [5ms]
2025-06-14 09:40:24.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:24.391 [info] > git status -z -uall [4ms]
2025-06-14 09:40:24.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:29.406 [info] > git config --get commit.template [6ms]
2025-06-14 09:40:29.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:29.417 [info] > git status -z -uall [4ms]
2025-06-14 09:40:29.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:34.433 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:34.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:34.449 [info] > git status -z -uall [7ms]
2025-06-14 09:40:34.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:40:39.461 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:39.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:39.471 [info] > git status -z -uall [6ms]
2025-06-14 09:40:39.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:40:44.490 [info] > git config --get commit.template [8ms]
2025-06-14 09:40:44.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:44.500 [info] > git status -z -uall [4ms]
2025-06-14 09:40:44.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:49.514 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:49.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:49.522 [info] > git status -z -uall [3ms]
2025-06-14 09:40:49.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:40:54.532 [info] > git config --get commit.template [3ms]
2025-06-14 09:40:54.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:40:54.544 [info] > git status -z -uall [3ms]
2025-06-14 09:40:54.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:40:59.556 [info] > git config --get commit.template [4ms]
2025-06-14 09:40:59.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:40:59.565 [info] > git status -z -uall [4ms]
2025-06-14 09:40:59.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:42:49.027 [info] > git config --get commit.template [5ms]
2025-06-14 09:42:49.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:42:49.037 [info] > git status -z -uall [5ms]
2025-06-14 09:42:49.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:43:29.852 [info] > git config --get commit.template [2ms]
2025-06-14 09:43:29.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:43:29.868 [info] > git status -z -uall [6ms]
2025-06-14 09:43:29.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:43:34.879 [info] > git config --get commit.template [4ms]
2025-06-14 09:43:34.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:43:34.887 [info] > git status -z -uall [3ms]
2025-06-14 09:43:34.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:43:39.910 [info] > git config --get commit.template [10ms]
2025-06-14 09:43:39.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 09:43:39.926 [info] > git status -z -uall [8ms]
2025-06-14 09:43:39.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:43:44.948 [info] > git config --get commit.template [9ms]
2025-06-14 09:43:44.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:43:44.964 [info] > git status -z -uall [7ms]
2025-06-14 09:43:44.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:43:49.979 [info] > git config --get commit.template [5ms]
2025-06-14 09:43:49.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:43:49.988 [info] > git status -z -uall [4ms]
2025-06-14 09:43:49.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:43:54.998 [info] > git config --get commit.template [2ms]
2025-06-14 09:43:55.003 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:43:55.012 [info] > git status -z -uall [6ms]
2025-06-14 09:43:55.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:44:00.024 [info] > git config --get commit.template [4ms]
2025-06-14 09:44:00.025 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:44:00.033 [info] > git status -z -uall [4ms]
2025-06-14 09:44:00.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:45:25.296 [info] > git config --get commit.template [4ms]
2025-06-14 09:45:25.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:45:25.305 [info] > git status -z -uall [4ms]
2025-06-14 09:45:25.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:45:30.316 [info] > git config --get commit.template [4ms]
2025-06-14 09:45:30.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:45:30.327 [info] > git status -z -uall [4ms]
2025-06-14 09:45:30.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:45:35.339 [info] > git config --get commit.template [2ms]
2025-06-14 09:45:35.347 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:45:35.356 [info] > git status -z -uall [4ms]
2025-06-14 09:45:35.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:08.571 [info] > git config --get commit.template [4ms]
2025-06-14 09:47:08.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:08.580 [info] > git status -z -uall [4ms]
2025-06-14 09:47:08.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:13.593 [info] > git config --get commit.template [5ms]
2025-06-14 09:47:13.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:13.602 [info] > git status -z -uall [5ms]
2025-06-14 09:47:13.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:18.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:18.616 [info] > git config --get commit.template [7ms]
2025-06-14 09:47:18.623 [info] > git status -z -uall [3ms]
2025-06-14 09:47:18.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:47:23.636 [info] > git config --get commit.template [5ms]
2025-06-14 09:47:23.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:23.643 [info] > git status -z -uall [3ms]
2025-06-14 09:47:23.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:47:28.663 [info] > git config --get commit.template [10ms]
2025-06-14 09:47:28.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:47:28.675 [info] > git status -z -uall [4ms]
2025-06-14 09:47:28.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:47:33.687 [info] > git config --get commit.template [5ms]
2025-06-14 09:47:33.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:33.694 [info] > git status -z -uall [3ms]
2025-06-14 09:47:33.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:47:38.705 [info] > git config --get commit.template [3ms]
2025-06-14 09:47:38.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:38.714 [info] > git status -z -uall [4ms]
2025-06-14 09:47:38.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:43.725 [info] > git config --get commit.template [3ms]
2025-06-14 09:47:43.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:47:43.733 [info] > git status -z -uall [3ms]
2025-06-14 09:47:43.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:48.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:48.750 [info] > git config --get commit.template [7ms]
2025-06-14 09:47:48.761 [info] > git status -z -uall [5ms]
2025-06-14 09:47:48.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:53.774 [info] > git config --get commit.template [4ms]
2025-06-14 09:47:53.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:47:53.784 [info] > git status -z -uall [4ms]
2025-06-14 09:47:53.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:47:58.800 [info] > git config --get commit.template [5ms]
2025-06-14 09:47:58.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:47:58.813 [info] > git status -z -uall [6ms]
2025-06-14 09:47:58.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:48:03.825 [info] > git config --get commit.template [2ms]
2025-06-14 09:48:03.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:48:03.843 [info] > git status -z -uall [4ms]
2025-06-14 09:48:03.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:48:08.865 [info] > git config --get commit.template [5ms]
2025-06-14 09:48:08.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-14 09:48:08.921 [info] > git status -z -uall [13ms]
2025-06-14 09:48:08.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-14 09:48:20.530 [info] > git config --get commit.template [2ms]
2025-06-14 09:48:20.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:48:20.553 [info] > git status -z -uall [5ms]
2025-06-14 09:48:20.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:48:25.567 [info] > git config --get commit.template [4ms]
2025-06-14 09:48:25.568 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:48:25.576 [info] > git status -z -uall [4ms]
2025-06-14 09:48:25.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:48:30.592 [info] > git config --get commit.template [6ms]
2025-06-14 09:48:30.593 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:48:30.601 [info] > git status -z -uall [4ms]
2025-06-14 09:48:30.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:48:37.087 [info] > git config --get commit.template [34ms]
2025-06-14 09:48:37.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-14 09:48:37.119 [info] > git status -z -uall [9ms]
2025-06-14 09:48:37.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-14 09:48:57.663 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-14 09:48:57.664 [info] > git config --get commit.template [7ms]
2025-06-14 09:48:57.677 [info] > git status -z -uall [7ms]
2025-06-14 09:48:57.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:52:30.470 [info] > git config --get commit.template [3ms]
2025-06-14 09:52:30.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:52:30.479 [info] > git status -z -uall [4ms]
2025-06-14 09:52:30.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:52:35.491 [info] > git config --get commit.template [4ms]
2025-06-14 09:52:35.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:52:35.499 [info] > git status -z -uall [3ms]
2025-06-14 09:52:35.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:52:40.511 [info] > git config --get commit.template [4ms]
2025-06-14 09:52:40.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:52:40.520 [info] > git status -z -uall [4ms]
2025-06-14 09:52:40.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:52:45.532 [info] > git config --get commit.template [3ms]
2025-06-14 09:52:45.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:52:45.545 [info] > git status -z -uall [8ms]
2025-06-14 09:52:45.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-14 09:53:04.832 [info] > git config --get commit.template [3ms]
2025-06-14 09:53:04.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:04.840 [info] > git status -z -uall [3ms]
2025-06-14 09:53:04.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:53:09.852 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:09.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:09.861 [info] > git status -z -uall [4ms]
2025-06-14 09:53:09.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:53:14.875 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:14.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:53:14.884 [info] > git status -z -uall [4ms]
2025-06-14 09:53:14.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:53:19.899 [info] > git config --get commit.template [5ms]
2025-06-14 09:53:19.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:19.918 [info] > git status -z -uall [11ms]
2025-06-14 09:53:19.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 09:53:24.933 [info] > git config --get commit.template [3ms]
2025-06-14 09:53:24.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:53:24.941 [info] > git status -z -uall [3ms]
2025-06-14 09:53:24.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:53:29.952 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:29.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:53:29.962 [info] > git status -z -uall [5ms]
2025-06-14 09:53:29.963 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:53:34.975 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:34.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:34.983 [info] > git status -z -uall [4ms]
2025-06-14 09:53:34.984 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:53:39.994 [info] > git config --get commit.template [3ms]
2025-06-14 09:53:39.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:40.003 [info] > git status -z -uall [4ms]
2025-06-14 09:53:40.004 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:53:45.017 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:45.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:53:45.032 [info] > git status -z -uall [5ms]
2025-06-14 09:53:45.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:53:50.043 [info] > git config --get commit.template [4ms]
2025-06-14 09:53:50.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:53:50.052 [info] > git status -z -uall [4ms]
2025-06-14 09:53:50.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:53:55.061 [info] > git config --get commit.template [2ms]
2025-06-14 09:53:55.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:53:55.073 [info] > git status -z -uall [3ms]
2025-06-14 09:53:55.074 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:54:00.091 [info] > git config --get commit.template [7ms]
2025-06-14 09:54:00.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:00.103 [info] > git status -z -uall [6ms]
2025-06-14 09:54:00.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:05.114 [info] > git config --get commit.template [3ms]
2025-06-14 09:54:05.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:05.122 [info] > git status -z -uall [3ms]
2025-06-14 09:54:05.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:11.502 [info] > git config --get commit.template [9ms]
2025-06-14 09:54:11.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 09:54:11.526 [info] > git status -z -uall [8ms]
2025-06-14 09:54:11.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:16.539 [info] > git config --get commit.template [2ms]
2025-06-14 09:54:16.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:16.552 [info] > git status -z -uall [4ms]
2025-06-14 09:54:16.553 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:21.560 [info] > git config --get commit.template [1ms]
2025-06-14 09:54:21.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:21.574 [info] > git status -z -uall [5ms]
2025-06-14 09:54:21.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:26.584 [info] > git config --get commit.template [3ms]
2025-06-14 09:54:26.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:26.594 [info] > git status -z -uall [4ms]
2025-06-14 09:54:26.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:54:31.606 [info] > git config --get commit.template [4ms]
2025-06-14 09:54:31.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:54:31.615 [info] > git status -z -uall [4ms]
2025-06-14 09:54:31.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:36.629 [info] > git config --get commit.template [5ms]
2025-06-14 09:54:36.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:36.636 [info] > git status -z -uall [3ms]
2025-06-14 09:54:36.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:54:41.648 [info] > git config --get commit.template [4ms]
2025-06-14 09:54:41.649 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:41.656 [info] > git status -z -uall [4ms]
2025-06-14 09:54:41.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:46.668 [info] > git config --get commit.template [4ms]
2025-06-14 09:54:46.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:46.678 [info] > git status -z -uall [6ms]
2025-06-14 09:54:46.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:54:51.692 [info] > git config --get commit.template [5ms]
2025-06-14 09:54:51.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:51.699 [info] > git status -z -uall [3ms]
2025-06-14 09:54:51.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:54:56.712 [info] > git config --get commit.template [4ms]
2025-06-14 09:54:56.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:54:56.722 [info] > git status -z -uall [5ms]
2025-06-14 09:54:56.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:55:01.732 [info] > git config --get commit.template [3ms]
2025-06-14 09:55:01.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:01.743 [info] > git status -z -uall [4ms]
2025-06-14 09:55:01.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 09:55:06.758 [info] > git config --get commit.template [7ms]
2025-06-14 09:55:06.759 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:06.771 [info] > git status -z -uall [6ms]
2025-06-14 09:55:06.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 09:55:11.789 [info] > git config --get commit.template [7ms]
2025-06-14 09:55:11.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:11.804 [info] > git status -z -uall [7ms]
2025-06-14 09:55:11.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-14 09:55:16.821 [info] > git config --get commit.template [2ms]
2025-06-14 09:55:16.838 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-14 09:55:16.859 [info] > git status -z -uall [11ms]
2025-06-14 09:55:16.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 09:55:21.876 [info] > git config --get commit.template [7ms]
2025-06-14 09:55:21.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:21.885 [info] > git status -z -uall [5ms]
2025-06-14 09:55:21.886 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:55:26.893 [info] > git config --get commit.template [2ms]
2025-06-14 09:55:26.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:55:26.906 [info] > git status -z -uall [6ms]
2025-06-14 09:55:26.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:55:31.918 [info] > git config --get commit.template [4ms]
2025-06-14 09:55:31.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:31.927 [info] > git status -z -uall [5ms]
2025-06-14 09:55:31.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:55:36.938 [info] > git config --get commit.template [3ms]
2025-06-14 09:55:36.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 09:55:36.946 [info] > git status -z -uall [3ms]
2025-06-14 09:55:36.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:55:41.959 [info] > git config --get commit.template [4ms]
2025-06-14 09:55:41.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:55:41.967 [info] > git status -z -uall [3ms]
2025-06-14 09:55:41.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 09:55:46.980 [info] > git config --get commit.template [4ms]
2025-06-14 09:55:46.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 09:55:46.990 [info] > git status -z -uall [5ms]
2025-06-14 09:55:46.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
