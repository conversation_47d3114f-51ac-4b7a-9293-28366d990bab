2025-06-14 09:28:47.553 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-14 09:28:47.553 [info] [Activation] Extension version: 0.111.**********
2025-06-14 09:28:48.229 [info] [Authentication] Creating hub for .com
2025-06-14 09:28:48.740 [info] [Activation] Looking for git repository
2025-06-14 09:28:48.740 [info] [Activation] Found 0 repositories during activation
2025-06-14 09:28:48.740 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-14 09:28:48.746 [info] [GitAPI] Registering git provider
2025-06-14 09:28:48.746 [info] [Review+0] Validate state in progress
2025-06-14 09:28:48.746 [info] [Review+0] Validating state...
2025-06-14 09:28:49.012 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-14 09:28:49.117 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 09:28:49.117 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 09:28:49.119 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-14 09:28:49.127 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 09:28:49.131 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 09:28:49.133 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-14 09:28:49.250 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-14 09:28:49.748 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 09:28:49.748 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-14 09:28:49.769 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-14 09:28:50.040 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-14 09:29:11.115 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-14 09:29:11.115 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-14 09:30:48.745 [error] [Review+0] Timeout occurred while validating state.
